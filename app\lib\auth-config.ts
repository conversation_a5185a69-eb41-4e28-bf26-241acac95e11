import { NextAuthOptions } from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import EmailProvider from 'next-auth/providers/email';
import { CustomPrismaAdapter } from './prisma-adapter';
import { sendVerificationRequest } from './email';



const googleClientId = process.env.GOOGLE_CLIENT_ID || '';
const googleClientSecret = process.env.GOOGLE_CLIENT_SECRET || '';
const githubClientId = process.env.GITHUB_ID || '';
const githubClientSecret = process.env.GITHUB_SECRET || '';
const resendApiKey = process.env.RESEND_API_KEY || '';
const resendSender = process.env.RESEND_SENDER || '';

if (!googleClientId || !googleClientSecret) {
  throw new Error('Missing Google OAuth credentials');
}

if (!githubClientId || !githubClientSecret) {
  throw new Error('Missing GitHub OAuth credentials');
}

if (!resendApiKey || !resendSender) {
  throw new Error('Missing Resend email credentials');
}

export const authOptions: NextAuthOptions = {
  adapter: CustomPrismaAdapter(),
  // Allow linking accounts with the same email address
  allowDangerousEmailAccountLinking: true,
  providers: [
    GoogleProvider({
      clientId: googleClientId,
      clientSecret: googleClientSecret,
    }),
    GitHubProvider({
      clientId: githubClientId,
      clientSecret: githubClientSecret,
    }),
    EmailProvider({
      server: {
        host: 'smtp.resend.com',
        port: 587,
        auth: {
          user: 'resend',
          pass: resendApiKey,
        },
      },
      from: resendSender,
      sendVerificationRequest,
      // Extend magic link expiration to 24 hours
      maxAge: 24 * 60 * 60, // 24 hours in seconds
      // Ensure proper URL generation for development
      generateVerificationToken: undefined, // Use default
    }),
  ],
  session: {
    strategy: 'jwt',
    // Extend session duration to 30 days
    maxAge: 30 * 24 * 60 * 60, // 30 days in seconds
    // Update session every 24 hours
    updateAge: 24 * 60 * 60, // 24 hours in seconds
  },
  pages: {
    signIn: '/login',
    error: '/login',
    verifyRequest: '/login', // Redirect here after email is sent
  },
  // Ensure proper URL handling
  useSecureCookies: process.env.NODE_ENV === 'production',
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.session-token' : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    }
  },
  callbacks: {
    async signIn({ user, account, profile, email, credentials }) {
      // Debug logging for OAuth sign-in attempts
      console.log('SignIn callback:', {
        user: user?.email,
        provider: account?.provider,
        accountId: account?.providerAccountId,
        userExists: !!user?.id,
      });

      // Log profile information for debugging
      if (profile) {
        console.log('Profile data:', {
          email: profile.email,
          name: profile.name,
          provider: account?.provider
        });
      }

      return true;
    },
    async jwt({ token, user, account }) {
      // Include user info in JWT token on sign in
      if (user) {
        token.id = user.id;
        token.provider = account?.provider;
      }
      return token;
    },
    async session({ session, token }) {
      // Include user info from JWT token in session
      if (session.user && token) {
        session.user.id = token.id as string;
        session.user.provider = token.provider as string;
      }
      return session;
    },
    async redirect({ baseUrl }) {
      // Always redirect to dashboard after successful authentication
      return `${baseUrl}/dashboard`;
    },
  },
};
